# Backward Compatibility Strategy for getTaskManagementInfo API

## Overview

This document outlines the strategy implemented to maintain backward compatibility between the old SQL-based `getTaskManagementInfo` API and the new TypeORM-based implementation.

## Key Differences Between Old and New APIs

### Old API (SQL-based)
- **Endpoint**: Uses raw SQL queries with complex subqueries
- **Parameters**: `page`, `size`, `order_by`, `filters` (JSON string)
- **Response Format**: 
  ```json
  {
    "data": [...],
    "pagination": {
      "pageNumber": 1,
      "pageSize": 10,
      "totalElements": 100,
      "totalPages": 10
    }
  }
  ```
- **Data Structure**: Complex nested objects with specific field names like `assigned_to_info`, `involve_in_users`, `worksheet_list`, `item_list`, `prod_quantity_list`, `todo_list`

### New API (TypeORM-based)
- **Endpoint**: Uses TypeORM QueryBuilder for better performance
- **Parameters**: `page`, `size`, `orderBy`, `filters`, `stateId`, `templateId`, `status`, `assignedTo`, `taskType`
- **Response Format**: 
  ```json
  {
    "data": [...],
    "page": 1,
    "size": 10,
    "total": 100
  }
  ```
- **Data Structure**: Simplified structure with `taskChainInfo`, `productionInfo`, `warehouseItemsInfo`

## Backward Compatibility Implementation

### 1. Response Format Transformation

The new API includes a `formatTasksForBackwardCompatibility` method that transforms the TypeORM response to match the old API structure exactly:

```typescript
private async formatTasksForBackwardCompatibility(tasks: any[], user: ICurrentUser): Promise<any[]>
```

This method:
- Maps new field names to old field names
- Formats dates to match old API format (`YYYY-MM-DD HH24:MI:SS`)
- Reconstructs nested objects like `assigned_to_info`, `item_list`, `prod_quantity_list`
- Calculates `todo_total` and `todo_done` counts
- Includes task transfer information for ENV_POUR tasks

### 2. Parameter Compatibility

The controller supports both old and new parameter names:
- `order_by` (old) and `orderBy` (new)
- Maintains support for `filters` as JSON string
- Added legacy endpoint `/task-management-info-legacy` for complete backward compatibility

### 3. Pagination Format

The response is transformed to match the old pagination structure:
```typescript
const pagination = {
  pageNumber: parseInt(page.toString()),
  pageSize: parseInt(size.toString()),
  totalElements: total,
  totalPages: totalPages,
};

return {
  data: formattedTasks,
  pagination: pagination,
};
```

### 4. Data Field Mapping

| Old API Field | New API Source | Notes |
|---------------|----------------|-------|
| `assigned_to_info` | `getAssignedToInfoForTask()` | User information array |
| `involve_in_users` | `getInvolveInUsersForTask()` | Involved users array |
| `worksheet_list` | `getWorksheetListForTask()` | Worksheet data |
| `item_list` | `warehouseItemsInfo.items` | Warehouse items with UOM info |
| `prod_quantity_list` | `productionInfo.items` | Production quantities |
| `todo_list` | `getTodoListForTask()` | Todo items |
| `todo_total` | Calculated from todo_list | Count of all todos |
| `todo_done` | Calculated from todo_list | Count of completed todos |
| `task_transfers` | `taskChainInfo.itemsByParent` | For ENV_POUR tasks only |

## Usage Examples

### Using the New API (Recommended)
```typescript
GET /seasonal-management/farming-plan-task/task-management-info?page=1&size=10&orderBy=creation:DESC
```

### Using Legacy Compatibility
```typescript
GET /seasonal-management/farming-plan-task/task-management-info?page=1&size=10&order_by=creation:DESC
```

### Using Full Legacy Endpoint
```typescript
GET /seasonal-management/farming-plan-task/task-management-info-legacy?page=1&size=10&order_by=creation:DESC
```

## Migration Path

1. **Phase 1**: Both APIs work simultaneously
2. **Phase 2**: Frontend gradually migrates to new parameter names
3. **Phase 3**: Legacy endpoint can be deprecated after full migration

## Benefits

1. **Zero Breaking Changes**: Existing frontend code continues to work
2. **Improved Performance**: TypeORM provides better query optimization
3. **Better Maintainability**: Cleaner code structure with TypeORM
4. **Enhanced Features**: Support for new filtering options
5. **Task Linking Support**: Includes new task transfer functionality

## Notes

- Helper methods like `getTodoListForTask()`, `getWorksheetListForTask()` return empty arrays for now
- These can be implemented based on actual entity structures when needed
- The strategy prioritizes compatibility over performance for the transformation layer
- Consider caching for frequently accessed data if performance becomes an issue
