# Debug Information for Production Plan Filter

## Overview
When using the `productionPlanId` filter, the API response now includes debug information to help verify that the filter is working correctly.

## Debug Info Structure

### When Production Plan Filter is Applied
```json
{
  "data": [
    {
      "name": "TASK-001",
      "label": "Task Label",
      // ... other task fields
      "debug_info": {
        "productionPlanFilter": {
          "applied": true,
          "productionPlanId": "PLAN-001",
          "matchingYieldOutput": {
            "name": "YIELD-OUTPUT-001",
            "production_plan_id": "PLAN-001",
            "environment_template_id": "ENV-TEMPLATE-001",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "yield_value": 1000,
            "expected_output": 800,
            "allocated_quantity": 200,
            "remaining_quantity": 600
          },
          "matchingProductionPlan": {
            "name": "PLAN-001",
            "label": "Spring 2024 Production Plan",
            "customer_id": "CUSTOMER-001",
            "plant_id": "PLANT-001",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "status": "assigned"
          }
        }
      }
    }
  ],
  "pagination": {
    "pageNumber": 1,
    "pageSize": 10,
    "totalElements": 1,
    "totalPages": 1
  }
}
```

### When Production Plan Filter is NOT Applied
```json
{
  "data": [
    {
      "name": "TASK-001",
      "label": "Task Label",
      // ... other task fields
      "debug_info": {
        "productionPlanFilter": {
          "applied": false
        }
      }
    }
  ]
}
```

## How to Use Debug Info

### 1. Verify Filter is Applied
Check if `debug_info.productionPlanFilter.applied` is `true` when you pass `productionPlanId` parameter.

### 2. Check Matching Logic
- **Environment Template Match**: Verify that `matchingYieldOutput.environment_template_id` matches the task's `environment_template_id`
- **Date Overlap**: Check that the yield output date range overlaps with the task date range
- **Production Plan**: Confirm that `matchingYieldOutput.production_plan_id` equals the requested `productionPlanId`

### 3. Debug Missing Tasks
If expected tasks are not returned:
1. Check if `matchingYieldOutput` is `null` - this means no yield output was found for the task
2. Verify the environment template ID matches
3. Check date ranges for overlap
4. Confirm the production plan belongs to the correct customer

### 4. Debug Allocation Issues
Use the yield output information to understand allocation:
- `yield_value`: Total expected yield
- `expected_output`: Expected final output
- `allocated_quantity`: Currently allocated amount
- `remaining_quantity`: Available for new allocations

## Example API Calls for Testing

### Test with Valid Production Plan
```bash
curl -X GET "http://localhost:3000/api/farming-plan-task/task-management-info?productionPlanId=PLAN-001&page=1&size=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Test without Production Plan Filter
```bash
curl -X GET "http://localhost:3000/api/farming-plan-task/task-management-info?page=1&size=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Test with Non-existent Production Plan
```bash
curl -X GET "http://localhost:3000/api/farming-plan-task/task-management-info?productionPlanId=NON-EXISTENT&page=1&size=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Troubleshooting

### No Tasks Returned
1. **Check debug_info**: If `applied: true` but `matchingYieldOutput: null`, the issue is with the matching logic
2. **Verify data**: Ensure yield outputs exist for the production plan
3. **Check dates**: Verify task and yield output date ranges overlap
4. **Environment templates**: Confirm environment template IDs match

### Unexpected Tasks Returned
1. **Check filter logic**: Verify the EXISTS query is working correctly
2. **Date overlap**: Check if date ranges are overlapping when they shouldn't
3. **Customer isolation**: Ensure tasks belong to the correct customer

### Performance Issues
1. **Index usage**: Check if proper indexes exist on environment_template_id and date fields
2. **Query optimization**: Monitor the EXISTS subquery performance
3. **Data volume**: Consider pagination for large datasets

## Debug Info Fields Explanation

### matchingYieldOutput
- `name`: Unique identifier of the yield output
- `production_plan_id`: ID of the production plan this yield output belongs to
- `environment_template_id`: Environment template that matches the task
- `start_date`/`end_date`: Date range for the yield output
- `yield_value`: Expected yield quantity
- `expected_output`: Expected final output quantity
- `allocated_quantity`: Amount already allocated to tasks
- `remaining_quantity`: Amount available for new allocations

### matchingProductionPlan
- `name`: Unique identifier of the production plan
- `label`: Human-readable name of the production plan
- `customer_id`: Customer who owns this production plan
- `plant_id`: Plant where this production plan is executed
- `start_date`/`end_date`: Overall date range for the production plan
- `status`: Current status (plan, assigned, completed)

This debug information helps ensure the production plan filter is working correctly and provides visibility into the matching logic.
