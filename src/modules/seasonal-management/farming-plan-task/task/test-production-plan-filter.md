# Test Production Plan Filter for getTaskManagementInfo

## Overview
This document describes how to test the new production plan filter functionality added to the `getTaskManagementInfo` API.

## API Changes

### New Parameter
- `productionPlanId` (optional): Filter tasks by production plan yield output

### Usage Examples

#### 1. Filter by Production Plan ID
```bash
GET /api/farming-plan-task/task-management-info?productionPlanId=PLAN-001&page=1&size=10
```

#### 2. Combine with other filters
```bash
GET /api/farming-plan-task/task-management-info?productionPlanId=PLAN-001&taskType=ENV_POUR&status=In Progress
```

#### 3. Using dynamic filters with production plan
```bash
GET /api/farming-plan-task/task-management-info?productionPlanId=PLAN-001&filters=[["task","status","=","In Progress"]]
```

## Filter Logic

The filter works by:
1. Joining tasks with production plan yield outputs
2. Matching on environment template ID
3. Checking date overlap between task and yield output periods
4. Filtering by the specified production plan ID
5. Ensuring customer authorization

### SQL Logic
```sql
EXISTS (
  SELECT 1 FROM production_plan_yield_expected_output yieldOutput
  INNER JOIN production_plan productionPlan ON yieldOutput.production_plan_id = productionPlan.name
  WHERE yieldOutput.environment_template_id = task.environment_template_id
    AND yieldOutput.production_plan_id = :productionPlanId
    AND productionPlan.customer_id = :customer_id
    AND yieldOutput.start_date <= task.end_date 
    AND yieldOutput.end_date >= task.start_date
)
```

## Test Cases

### 1. Basic Filter Test
- Create a production plan with yield outputs
- Create tasks with matching environment templates and date ranges
- Call API with productionPlanId parameter
- Verify only matching tasks are returned

### 2. Date Overlap Test
- Create tasks with various date ranges
- Create yield outputs with specific date ranges
- Test that only tasks with overlapping dates are returned

### 3. Environment Template Matching
- Create tasks with different environment templates
- Create yield outputs for specific environment templates
- Verify only tasks with matching environment templates are returned

### 4. Customer Authorization Test
- Verify that tasks from other customers are not returned
- Test with different customer contexts

### 5. Combined Filters Test
- Test production plan filter combined with other filters
- Verify all filters work together correctly

## Expected Behavior

1. **When productionPlanId is provided**: Only return tasks that have yield outputs in the specified production plan
2. **When productionPlanId is null/undefined**: Return all tasks (no additional filtering)
3. **Date overlap logic**: Tasks and yield outputs must have overlapping date ranges
4. **Environment template matching**: Task environment_template_id must match yield output environment_template_id
5. **Customer isolation**: Only return tasks belonging to the current customer

## Backward Compatibility

- The new parameter is optional, so existing API calls continue to work
- Both new endpoint and legacy endpoint support the filter
- Response format remains unchanged
