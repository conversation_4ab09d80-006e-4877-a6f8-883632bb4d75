# Production Plan Filter Examples

## API Endpoint
```
GET /api/farming-plan-task/task-management-info
```

## New Parameter
- `productionPlanId` (optional): Filter tasks by production plan yield output

## Usage Examples

### 1. Basic Production Plan Filter
```bash
curl -X GET "http://localhost:3000/api/farming-plan-task/task-management-info?productionPlanId=PLAN-001" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. Production Plan Filter with Pagination
```bash
curl -X GET "http://localhost:3000/api/farming-plan-task/task-management-info?productionPlanId=PLAN-001&page=1&size=20" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. Combined with Task Type Filter
```bash
curl -X GET "http://localhost:3000/api/farming-plan-task/task-management-info?productionPlanId=PLAN-001&taskType=ENV_POUR" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 4. Combined with Status Filter
```bash
curl -X GET "http://localhost:3000/api/farming-plan-task/task-management-info?productionPlanId=PLAN-001&status=In%20Progress" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 5. Using Dynamic Filters with Production Plan
```bash
curl -X GET "http://localhost:3000/api/farming-plan-task/task-management-info?productionPlanId=PLAN-001&filters=%5B%5B%22task%22%2C%22status%22%2C%22%3D%22%2C%22In%20Progress%22%5D%5D" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

Note: The filters parameter is URL-encoded JSON: `[["task","status","=","In Progress"]]`

### 6. Complex Filter Example
```bash
curl -X GET "http://localhost:3000/api/farming-plan-task/task-management-info?productionPlanId=PLAN-001&taskType=ENV_POUR&assignedTo=USER-001&orderBy=task.start_date%20DESC" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 7. Legacy Endpoint Support
```bash
curl -X GET "http://localhost:3000/api/farming-plan-task/task-management-info-legacy" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "productionPlanId": "PLAN-001",
    "page": 1,
    "size": 10,
    "order_by": "task.start_date DESC"
  }'
```

## JavaScript/TypeScript Examples

### Using Fetch API
```typescript
const getTasksByProductionPlan = async (productionPlanId: string) => {
  const response = await fetch(
    `/api/farming-plan-task/task-management-info?productionPlanId=${productionPlanId}`,
    {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }
  );
  
  if (!response.ok) {
    throw new Error('Failed to fetch tasks');
  }
  
  return await response.json();
};
```

### Using Axios
```typescript
import axios from 'axios';

const getTasksByProductionPlan = async (productionPlanId: string, page = 1, size = 10) => {
  try {
    const response = await axios.get('/api/farming-plan-task/task-management-info', {
      params: {
        productionPlanId,
        page,
        size
      },
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error fetching tasks:', error);
    throw error;
  }
};
```

### Complex Filter with Multiple Parameters
```typescript
const getFilteredTasks = async (filters: {
  productionPlanId?: string;
  taskType?: string;
  status?: string;
  assignedTo?: string;
  page?: number;
  size?: number;
}) => {
  const params = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      params.append(key, value.toString());
    }
  });
  
  const response = await fetch(
    `/api/farming-plan-task/task-management-info?${params.toString()}`,
    {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }
  );
  
  return await response.json();
};

// Usage
const tasks = await getFilteredTasks({
  productionPlanId: 'PLAN-001',
  taskType: 'ENV_POUR',
  status: 'In Progress',
  page: 1,
  size: 20
});
```

## Response Format
The response format remains the same as the original API:

```json
{
  "data": [
    {
      "name": "TASK-001",
      "label": "Task Label",
      "task_type": "ENV_POUR",
      "status": "In Progress",
      "start_date": "2024-01-01 00:00:00",
      "end_date": "2024-01-31 23:59:59",
      "environment_template_id": "ENV-TEMPLATE-001",
      // ... other task fields
    }
  ],
  "pagination": {
    "pageNumber": 1,
    "pageSize": 10,
    "totalElements": 25,
    "totalPages": 3
  }
}
```

## Filter Logic
Tasks are filtered based on:
1. **Environment Template Match**: Task's environment_template_id matches yield output's environment_template_id
2. **Date Overlap**: Task date range overlaps with yield output date range
3. **Production Plan**: Yield output belongs to the specified production plan
4. **Customer Authorization**: Only tasks belonging to the current customer are returned
