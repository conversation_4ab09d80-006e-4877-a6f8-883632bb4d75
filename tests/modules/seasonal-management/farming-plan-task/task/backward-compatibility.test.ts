import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { FarmingPlanTaskService } from '../../../../../src/modules/seasonal-management/farming-plan-task/task/FarmingPlanTaskService';
import { ICurrentUser } from '../../../../../src/interfaces';
import { AppDataSource } from '../../../../../src/orm/dataSource';

// Mock AppDataSource
jest.mock('@app/orm/dataSource', () => ({
  AppDataSource: {
    getRepository: jest.fn()
  }
}));

// Mock Container - Container is the default export
jest.mock('typedi', () => {
  const mockContainer = {
    get: jest.fn()
  };
  return {
    default: mockContainer,
    Container: mockContainer,
    Service: () => (target: any) => target
  };
});

describe('FarmingPlanTaskService - Backward Compatibility', () => {
  let service: FarmingPlanTaskService;
  let mockUser: ICurrentUser;
  let mockRepository: any;
  let mockQueryBuilder: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create mock query builder with proper typing for getManyAndCount
    mockQueryBuilder = {
      createQueryBuilder: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      addSelect: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getManyAndCount: jest.fn()
    };

    // Set up the getManyAndCount mock separately to avoid type issues
    mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);

    // Create mock repository
    mockRepository = {
      createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
      findOne: jest.fn(),
      find: jest.fn(),
      save: jest.fn(),
      create: jest.fn()
    };

    // Mock AppDataSource.getRepository
    (AppDataSource.getRepository as jest.Mock).mockReturnValue(mockRepository);

    // Mock Container.get for dependencies
    const typedi = require('typedi');
    typedi.default.get.mockImplementation((_serviceClass: any) => {
      // Return mock services for dependencies
      return {};
    });

    service = new FarmingPlanTaskService();
    mockUser = {
      user_id: 'test-user',
      customer_id: 'test-customer',
      customer: { id: 1 },
      tenant: { name: 'test-tenant' }
    } as ICurrentUser;
  });

  describe('getTaskManagementInfo Response Format', () => {
    it('should return response in old API format', async () => {
      // Mock the database calls
      jest.spyOn(service as any, 'formatTasksForBackwardCompatibility').mockResolvedValue([
        {
          name: 'task-1',
          label: 'Test Task',
          assigned_to_info: [{ name: 'user1', first_name: 'John', last_name: 'Doe' }],
          item_list: [],
          prod_quantity_list: [],
          todo_list: [],
          todo_total: 0,
          todo_done: 0,
          start_date: '2024-01-01 00:00:00',
          end_date: '2024-01-02 00:00:00'
        }
      ]);

      const result = await service.getTaskManagementInfo(mockUser, 1, 10);

      // Verify response structure matches old API
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('pagination');
      expect(result.pagination).toHaveProperty('pageNumber');
      expect(result.pagination).toHaveProperty('pageSize');
      expect(result.pagination).toHaveProperty('totalElements');
      expect(result.pagination).toHaveProperty('totalPages');
    });

    it('should format task data to match old API structure', async () => {
      const mockTask = {
        name: 'task-1',
        label: 'Test Task',
        assigned_to: 'user1',
        task_progress: 50,
        start_date: new Date('2024-01-01'),
        end_date: new Date('2024-01-02'),
        warehouseItemsInfo: {
          items: [{
            name: 'item-1',
            quantity: 10,
            exp_quantity: 15,
            iot_category_id: 'cat-1'
          }]
        },
        productionInfo: {
          items: [{
            name: 'prod-1',
            quantity: 5,
            exp_quantity: 8,
            product_id: 'prod-cat-1'
          }]
        }
      };

      const formatted = await (service as any).formatTasksForBackwardCompatibility([mockTask], mockUser);

      expect(formatted[0]).toHaveProperty('name', 'task-1');
      expect(formatted[0]).toHaveProperty('label', 'Test Task');
      expect(formatted[0]).toHaveProperty('task_progress', 50);
      expect(formatted[0]).toHaveProperty('item_list');
      expect(formatted[0]).toHaveProperty('prod_quantity_list');
      expect(formatted[0]).toHaveProperty('assigned_to_info');
      expect(formatted[0]).toHaveProperty('todo_total');
      expect(formatted[0]).toHaveProperty('todo_done');

      // Verify date format
      expect(formatted[0].start_date).toMatch(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/);
      expect(formatted[0].end_date).toMatch(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/);
    });
  });

  describe('Parameter Compatibility', () => {
    it('should handle old parameter names', async () => {
      // This would test that order_by parameter is properly handled
      const mockResult = {
        data: [],
        pagination: {
          pageNumber: 1,
          pageSize: 10,
          totalElements: 0,
          totalPages: 0
        }
      };

      jest.spyOn(service, 'getTaskManagementInfo').mockResolvedValue(mockResult);

      const result = await service.getTaskManagementInfo(
        mockUser,
        1,
        10,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        'creation:DESC' // old order_by format
      );

      expect(result).toEqual(mockResult);
    });
  });

  describe('Data Field Mapping', () => {
    it('should map warehouse items to item_list format', () => {
      // This test verifies the mapping logic in formatTasksForBackwardCompatibility
      // Example warehouse item structure:
      // {
      //   name: 'item-1',
      //   quantity: 10,
      //   exp_quantity: 15,
      //   loss_quantity: 1,
      //   issued_quantity: 9,
      //   draft_quantity: 0,
      //   iot_category_id: 'cat-1',
      //   category_name: 'Category 1',
      //   category_label: 'Category Label',
      //   active_uom: 'kg',
      //   uom_name: 'Kilogram',
      //   active_conversion_factor: 1
      // }

      // Expected format after mapping:
      // {
      //   name: 'item-1',
      //   quantity: 10,
      //   exp_quantity: 15,
      //   loss_quantity: 1,
      //   issued_quantity: 9,
      //   draft_quantity: 0,
      //   task_id: 'task-1',
      //   iot_category_id: 'cat-1',
      //   label: 'Category Label',
      //   item_name: 'Category 1',
      //   active_uom: 'kg',
      //   active_uom_name: 'Kilogram',
      //   active_conversion_factor: 1,
      //   uom_name: 'Kilogram',
      //   uom_id: 'kg',
      //   conversion_factor: 1,
      //   uoms: []
      // }

      expect(true).toBe(true); // Placeholder - actual implementation would test the mapping
    });

    it('should map production quantities to prod_quantity_list format', () => {
      // This test verifies the production quantity mapping
      // Example production item structure:
      // {
      //   name: 'prod-1',
      //   quantity: 5,
      //   exp_quantity: 8,
      //   lost_quantity: 0,
      //   issued_quantity: 5,
      //   draft_quantity: 0,
      //   finished_quantity: 5,
      //   product_id: 'prod-cat-1',
      //   product_name: 'Product 1',
      //   product_label: 'Product Label',
      //   active_uom: 'pcs',
      //   uom_name: 'Pieces',
      //   active_conversion_factor: 1
      // }

      expect(true).toBe(true); // Placeholder - actual implementation would test the mapping
    });
  });

  describe('ENV_POUR Task Transfer Support', () => {
    it('should include task_transfers for ENV_POUR tasks', async () => {
      const envPourTask = {
        name: 'env-pour-task',
        task_type: 'ENV_POUR',
        taskChainInfo: {
          itemsByParent: {
            'parent-task-1': [
              { itemId: 'item-1', quantity: 10 }
            ],
            'parent-task-2': [
              { itemId: 'item-2', quantity: 5 }
            ]
          }
        }
      };

      const formatted = await (service as any).formatTasksForBackwardCompatibility([envPourTask], mockUser);

      expect(formatted[0]).toHaveProperty('task_transfers');
      expect(formatted[0].task_transfers).toHaveLength(2);
      expect(formatted[0].task_transfers[0]).toHaveProperty('source_task_id');
      expect(formatted[0].task_transfers[0]).toHaveProperty('items');
    });

    it('should not include task_transfers for non-ENV_POUR tasks', async () => {
      const regularTask = {
        name: 'regular-task',
        task_type: 'OTHER',
        taskChainInfo: {
          itemsByParent: {
            'parent-task-1': [{ itemId: 'item-1', quantity: 10 }]
          }
        }
      };

      const formatted = await (service as any).formatTasksForBackwardCompatibility([regularTask], mockUser);

      expect(formatted[0]).not.toHaveProperty('task_transfers');
    });
  });
});
