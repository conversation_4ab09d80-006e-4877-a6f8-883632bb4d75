/// <reference types="jest" />
import 'reflect-metadata';
import { Container } from 'typedi';
import { DataSource } from 'typeorm';

// Extend global namespace for test utilities
declare global {
  var mockDataSource: jest.Mocked<DataSource>;
  namespace NodeJS {
    interface Global {
      mockDataSource: jest.Mocked<DataSource>;
    }
  }
}

// Mock logger to prevent console spam during tests
jest.mock('../src/loaders/logger', () => ({
  default: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
}));

// Create comprehensive mock DataSource
const createMockDataSource = (): jest.Mocked<DataSource> => ({
  getRepository: jest.fn(),
  transaction: jest.fn(),
  manager: {
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    delete: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    count: jest.fn(),
    query: jest.fn(),
  },
  isInitialized: true,
  initialize: jest.fn(),
  destroy: jest.fn(),
  synchronize: jest.fn(),
  dropDatabase: jest.fn(),
  runMigrations: jest.fn(),
  undoLastMigration: jest.fn(),
  hasMetadata: jest.fn(),
  getMetadata: jest.fn(),
  createQueryBuilder: jest.fn(),
  createQueryRunner: jest.fn(),
} as any);

// Global mock DataSource instance
global.mockDataSource = createMockDataSource();

// Mock AppDataSource module
jest.mock('../src/orm/dataSource', () => ({
  AppDataSource: global.mockDataSource,
}));

// Global test utilities and setup
beforeEach(() => {
  jest.clearAllMocks();
  Container.reset();

  // Reset mock DataSource to clean state
  global.mockDataSource = createMockDataSource();
});

// Global test timeout
jest.setTimeout(30000);
